const mongoose = require('mongoose');

const recordSchema = new mongoose.Schema({
  sourceId: { type: String, required: true },
  source: { type: String, required: true },
  entityType: { 
    type: String, 
    required: true,
    enum: ['pharmacy', 'doctor', 'clinic', 'hospital', 'other']
  },
  originalData: {
    name: { type: String, required: true },
    address: String,
    phone: String,
    license: String,
    metadata: mongoose.Schema.Types.Mixed
  },
  normalizedData: {
    name: String,
    nameTokens: [String],
    address: String,
    addressTokens: [String],
    phone: String,
    transliterations: {
      nameArabic: String,
      nameEnglish: String,
      addressArabic: String,
      addressEnglish: String
    }
  },
  unifiedEntityId: { type: String, index: true },
  processingStatus: {
    type: String,
    enum: ['pending', 'processed', 'error'],
    default: 'pending'
  },
  confidence: { type: Number, min: 0, max: 1 },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

recordSchema.index({ 'normalizedData.name': 'text', 'normalizedData.address': 'text' });
recordSchema.index({ source: 1, sourceId: 1 }, { unique: true });

module.exports = mongoose.model('Record', recordSchema);