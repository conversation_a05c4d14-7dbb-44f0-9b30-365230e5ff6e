const mongoose = require('mongoose');

const unifiedEntitySchema = new mongoose.Schema({
  unifiedId: { type: String, required: true, unique: true },
  entityType: { type: String, required: true },
  canonicalData: {
    name: { type: String, required: true },
    address: String,
    phone: String,
    coordinates: {
      lat: Number,
      lng: Number
    }
  },
  aliases: [{
    name: String,
    language: { type: String, enum: ['ar', 'en', 'mixed'] },
    source: String
  }],
  sourceRecords: [{
    recordId: { type: mongoose.Schema.Types.ObjectId, ref: 'Record' },
    confidence: Number,
    isCanonical: { type: Boolean, default: false }
  }],
  statistics: {
    totalRecords: { type: Number, default: 0 },
    averageConfidence: { type: Number, default: 0 },
    lastUpdated: { type: Date, default: Date.now }
  },
  reviewStatus: {
    type: String,
    enum: ['auto_unified', 'pending_review', 'approved', 'rejected'],
    default: 'auto_unified'
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

unifiedEntitySchema.index({ unifiedId: 1 });
unifiedEntitySchema.index({ 'canonicalData.name': 'text' });

module.exports = mongoose.model('UnifiedEntity', unifiedEntitySchema);