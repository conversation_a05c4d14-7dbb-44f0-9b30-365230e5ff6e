const { transliterate } = require('transliteration');
const natural = require('natural');
const logger = require('../utils/logger.js');

class PreprocessingService {
  constructor() {
    this.stemmer = natural.PorterStemmer;
    this.arabicStopwords = ['صيدلية', 'دكتور', 'عيادة', 'مستشفى', 'شارع', 'طريق'];
    this.englishStopwords = ['pharmacy', 'doctor', 'clinic', 'hospital', 'street', 'road', 'dr'];
  }

  // Normalize Arabic text
  normalizeArabic(text) {
    if (!text) return '';
    
    return text
      // Remove diacritics
      .replace(/[\u064B-\u065F]/g, '')
      // Normalize letters
      .replace(/[أإآ]/g, 'ا')
      .replace(/[ة]/g, 'ه')
      .replace(/[ي]/g, 'ى')
      // Remove extra spaces
      .replace(/\s+/g, ' ')
      .trim()
      .toLowerCase();
  }

  // Normalize English text
  normalizeEnglish(text) {
    if (!text) return '';
    
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  // Detect language
  detectLanguage(text) {
    if (!text) return 'unknown';
    
    const arabicRegex = /[\u0600-\u06FF]/;
    const englishRegex = /[a-zA-Z]/;
    
    const hasArabic = arabicRegex.test(text);
    const hasEnglish = englishRegex.test(text);
    
    if (hasArabic && hasEnglish) return 'mixed';
    if (hasArabic) return 'ar';
    if (hasEnglish) return 'en';
    return 'unknown';
  }

  // Transliterate text
  transliterateText(text, fromLang, toLang) {
    if (!text) return '';
    
    try {
      if (fromLang === 'ar' && toLang === 'en') {
        return transliterate(text);
      } else if (fromLang === 'en' && toLang === 'ar') {
        // For reverse transliteration, we'd need a more sophisticated approach
        // This is a simplified version
        return text;
      }
      return text;
    } catch (error) {
      logger.error('Transliteration error:', error);
      return text;
    }
  }

  // Tokenize and remove stopwords
  tokenize(text, language) {
    if (!text) return [];
    
    const tokens = text.split(/\s+/);
    const stopwords = language === 'ar' ? this.arabicStopwords : this.englishStopwords;
    
    return tokens
      .filter(token => token.length > 1)
      .filter(token => !stopwords.includes(token.toLowerCase()))
      .map(token => this.stemmer.stem(token));
  }

  // Main preprocessing function
  async preprocessRecord(record) {
    try {
      const { name, address, phone } = record.originalData;
      
      // Detect languages
      const nameLanguage = this.detectLanguage(name);
      const addressLanguage = this.detectLanguage(address);
      
      // Normalize text
      const normalizedName = nameLanguage === 'ar' 
        ? this.normalizeArabic(name) 
        : this.normalizeEnglish(name);
        
      const normalizedAddress = addressLanguage === 'ar' 
        ? this.normalizeArabic(address) 
        : this.normalizeEnglish(address);
      
      // Generate transliterations
      const transliterations = {
        nameArabic: nameLanguage === 'en' ? name : this.normalizeArabic(name),
        nameEnglish: nameLanguage === 'ar' ? this.transliterateText(name, 'ar', 'en') : name,
        addressArabic: addressLanguage === 'en' ? address : this.normalizeArabic(address),
        addressEnglish: addressLanguage === 'ar' ? this.transliterateText(address, 'ar', 'en') : address
      };
      
      // Tokenize
      const nameTokens = this.tokenize(normalizedName, nameLanguage);
      const addressTokens = this.tokenize(normalizedAddress, addressLanguage);
      
      return {
        normalizedData: {
          name: normalizedName,
          nameTokens,
          address: normalizedAddress,
          addressTokens,
          phone: phone ? phone.replace(/\D/g, '') : '',
          transliterations
        }
      };
    } catch (error) {
      logger.error('Preprocessing error:', error);
      throw error;
    }
  }
}

module.exports = PreprocessingService;