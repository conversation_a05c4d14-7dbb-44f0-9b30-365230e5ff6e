const fuzz = require('fuzzball');
const natural = require('natural');
const logger = require('../utils/logger.js');

class SimilarityService {
  constructor() {
    this.metaphone = natural.Metaphone;
    this.levenshtein = natural.LevenshteinDistance;
    this.jaro = natural.JaroWinklerDistance;
  }

  // Calculate fuzzy string similarity
  calculateFuzzyScore(str1, str2) {
    if (!str1 || !str2) return 0;
    
    return Math.max(
      fuzz.ratio(str1, str2),
      fuzz.partial_ratio(str1, str2),
      fuzz.token_sort_ratio(str1, str2),
      fuzz.token_set_ratio(str1, str2)
    ) / 100;
  }

  // Calculate phonetic similarity
  calculatePhoneticScore(str1, str2) {
    if (!str1 || !str2) return 0;
    
    const phone1 = this.metaphone.process(str1);
    const phone2 = this.metaphone.process(str2);
    
    return phone1 === phone2 ? 1 : 0;
  }

  // Calculate Jaro-Winkler similarity
  calculateJaroScore(str1, str2) {
    if (!str1 || !str2) return 0;
    return this.jaro(str1, str2);
  }

  // Calculate token-based similarity
  calculateTokenSimilarity(tokens1, tokens2) {
    if (!tokens1?.length || !tokens2?.length) return 0;
    
    const set1 = new Set(tokens1);
    const set2 = new Set(tokens2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size; // Jaccard similarity
  }

  // Calculate comprehensive similarity score
  calculateSimilarity(record1, record2, weights = {}) {
    const defaultWeights = {
      name: 0.6,
      address: 0.3,
      phone: 0.1,
      fuzzy: 0.4,
      phonetic: 0.2,
      jaro: 0.2,
      token: 0.2
    };
    
    const w = { ...defaultWeights, ...weights };
    
    try {
      // Name similarity
      const nameScores = {
        fuzzy: this.calculateFuzzyScore(record1.normalizedData.name, record2.normalizedData.name),
        phonetic: this.calculatePhoneticScore(record1.normalizedData.name, record2.normalizedData.name),
        jaro: this.calculateJaroScore(record1.normalizedData.name, record2.normalizedData.name),
        token: this.calculateTokenSimilarity(record1.normalizedData.nameTokens, record2.normalizedData.nameTokens)
      };
      
      const nameScore = (
        nameScores.fuzzy * w.fuzzy +
        nameScores.phonetic * w.phonetic +
        nameScores.jaro * w.jaro +
        nameScores.token * w.token
      );
      
      // Address similarity
      const addressScores = {
        fuzzy: this.calculateFuzzyScore(record1.normalizedData.address, record2.normalizedData.address),
        token: this.calculateTokenSimilarity(record1.normalizedData.addressTokens, record2.normalizedData.addressTokens)
      };
      
      const addressScore = (
        addressScores.fuzzy * 0.6 +
        addressScores.token * 0.4
      );
      
      // Phone similarity
      const phoneScore = record1.normalizedData.phone && record2.normalizedData.phone
        ? (record1.normalizedData.phone === record2.normalizedData.phone ? 1 : 0)
        : 0;
      
      // Cross-language similarity (transliterations)
      const crossLangScore = Math.max(
        this.calculateFuzzyScore(record1.normalizedData.transliterations.nameEnglish, record2.normalizedData.name),
        this.calculateFuzzyScore(record1.normalizedData.name, record2.normalizedData.transliterations.nameEnglish),
        this.calculateFuzzyScore(record1.normalizedData.transliterations.nameArabic, record2.normalizedData.name),
        this.calculateFuzzyScore(record1.normalizedData.name, record2.normalizedData.transliterations.nameArabic)
      );
      
      // Final weighted score
      const finalScore = Math.max(
        nameScore * w.name + addressScore * w.address + phoneScore * w.phone,
        crossLangScore * w.name + addressScore * w.address + phoneScore * w.phone
      );
      
      return {
        overall: finalScore,
        breakdown: {
          name: nameScore,
          address: addressScore,
          phone: phoneScore,
          crossLanguage: crossLangScore,
          details: { nameScores, addressScores }
        }
      };
    } catch (error) {
      logger.error('Similarity calculation error:', error);
      return { overall: 0, breakdown: {} };
    }
  }

  // Find similar records using different strategies
  async findSimilarRecords(targetRecord, candidateRecords, threshold = 0.7) {
    const similarities = [];
    
    for (const candidate of candidateRecords) {
      if (candidate._id.toString() === targetRecord._id.toString()) continue;
      
      const similarity = this.calculateSimilarity(targetRecord, candidate);
      
      if (similarity.overall >= threshold) {
        similarities.push({
          record: candidate,
          similarity: similarity.overall,
          breakdown: similarity.breakdown,
          confidence: this.calculateConfidence(similarity.overall, similarity.breakdown)
        });
      }
    }
    
    return similarities.sort((a, b) => b.similarity - a.similarity);
  }

  // Calculate confidence based on multiple factors
  calculateConfidence(overallScore, breakdown) {
    // Higher confidence for records with multiple strong signals
    let confidence = overallScore;
    
    // Boost confidence if phone numbers match
    if (breakdown.phone === 1) {
      confidence = Math.min(1, confidence + 0.1);
    }
    
    // Boost confidence if both name and address are strong
    if (breakdown.name > 0.8 && breakdown.address > 0.6) {
      confidence = Math.min(1, confidence + 0.05);
    }
    
    // Boost confidence for cross-language matches
    if (breakdown.crossLanguage > 0.8) {
      confidence = Math.min(1, confidence + 0.08);
    }
    
    return confidence;
  }
}

module.exports = SimilarityService;